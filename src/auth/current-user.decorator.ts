import { createParamDecorator, ExecutionContext } from "@nestjs/common";
import { GqlExecutionContext } from "@nestjs/graphql";
import { CognitoUserDto } from "./dto/cognito-user.dto";

interface IRequestWithUser {
  user: CognitoUserDto;
}

function isObjectWithReq(value: unknown): value is { req: unknown } {
  return (
    value !== null &&
    typeof value === "object" &&
    "req" in value &&
    value.req !== null &&
    value.req !== undefined
  );
}

export const CurrentUser = createParamDecorator(
  (_data: unknown, context: ExecutionContext): CognitoUserDto => {
    // Handle both REST and GraphQL contexts
    const ctx = GqlExecutionContext.create(context);

    // Try to get request from GraphQL context first
    let request: IRequestWithUser | null = null;
    try {
      const gqlContext: unknown = ctx.getContext();
      if (isObjectWithReq(gqlContext)) {
        request = gqlContext.req as IRequestWithUser;
      }
    } catch {
      request = null;
    }

    // Fallback to HTTP context if GraphQL context doesn't have request
    if (!request) {
      request = context.switchToHttp().getRequest<IRequestWithUser>();
    }

    return request.user;
  },
);
