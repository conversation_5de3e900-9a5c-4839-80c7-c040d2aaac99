import { createParamDecorator, ExecutionContext } from "@nestjs/common";
import { GqlExecutionContext } from "@nestjs/graphql";
import { CognitoUserDto } from "./dto/cognito-user.dto";

export const CurrentUser = createParamDecorator(
  (data: unknown, context: ExecutionContext): CognitoUserDto => {
    // Handle both REST and GraphQL contexts
    const ctx = GqlExecutionContext.create(context);
    const request = ctx.getContext().req || context.switchToHttp().getRequest();
    return request.user as CognitoUserDto;
  },
);
