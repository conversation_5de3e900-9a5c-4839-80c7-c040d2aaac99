import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
} from "@nestjs/common";
import { Reflector } from "@nestjs/core";
import { GqlExecutionContext } from "@nestjs/graphql";
import { CognitoAuthService } from "./cognito-auth.service";
import { IS_PUBLIC_KEY } from "./public.decorator";

@Injectable()
export class CognitoAuthGuard implements CanActivate {
  constructor(
    private readonly cognitoAuthService: CognitoAuthService,
    private readonly reflector: Reflector,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // Check if route is marked as public
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      return true;
    }

    // Handle both REST and GraphQL contexts
    const ctx = GqlExecutionContext.create(context);
    const request = ctx.getContext().req || context.switchToHttp().getRequest();

    const user = await this.cognitoAuthService.getUserFromRequest(request);
    if (!user) {
      throw new UnauthorizedException("Authentication required");
    }

    // Attach user to request for @CurrentUser() decorator
    request.user = user;
    return true;
  }
}
