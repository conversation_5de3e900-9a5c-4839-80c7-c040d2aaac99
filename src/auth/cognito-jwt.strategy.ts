import {
  Inject,
  Injectable,
  Logger,
  UnauthorizedException,
} from "@nestjs/common";
import { Request } from "express";
import { CognitoJwtVerifier } from "aws-jwt-verify";
import { CognitoUserDto } from "./dto/cognito-user.dto";
import { plainToInstance } from "class-transformer";
import awsConfig from "src/config/configuration/aws.config";
import { ConfigType } from "@nestjs/config";

@Injectable()
export class CognitoAuthService {
  private readonly logger = new Logger(CognitoAuthService.name);
  private accessTokenVerifier: ReturnType<typeof CognitoJwtVerifier.create>;
  private idTokenVerifier: ReturnType<typeof CognitoJwtVerifier.create>;

  constructor(
    @Inject(awsConfig.KEY)
    private readonly config: ConfigType<typeof awsConfig>,
  ) {
    // Create verifiers for both token types
    this.accessTokenVerifier = CognitoJwtVerifier.create({
      userPoolId: this.config.cognitoUserPoolId ?? "",
      tokenUse: "access",
      clientId: this.config.cognitoClientId ?? "",
    });

    this.idTokenVerifier = CognitoJwtVerifier.create({
      userPoolId: this.config.cognitoUserPoolId ?? "",
      tokenUse: "id",
      clientId: this.config.cognitoClientId ?? "",
    });
  }

  extractTokensFromRequest(request: Request): {
    accessToken: string;
    idToken: string;
  } {
    const cookies: Record<string, string | undefined> = request.cookies;

    const { idToken, accessToken } = Object.entries(cookies).reduce(
      (acc, [key, value]) => {
        if (key.includes("idToken")) {
          acc.idToken = value ?? "";
        } else if (key.includes("accessToken")) {
          acc.accessToken = value ?? "";
        }
        return acc;
      },
      { idToken: "", accessToken: "" },
    );

    return { accessToken, idToken };
  }

  async verifyTokens(
    accessToken: string,
    idToken: string,
  ): Promise<CognitoUserDto> {
    try {
      // Verify access token (primary authorization)
      const accessPayload = await this.accessTokenVerifier.verify(accessToken);
      let email = accessPayload.email; // Might be undefined in access token
      let emailVerified = accessPayload.email_verified;

      // If we have ID token, verify it and get email from there
      if (idToken.length > 0) {
        try {
          const idPayload = await this.idTokenVerifier.verify(idToken);
          email = idPayload.email; // ID token always has email
          emailVerified = idPayload.email_verified;
        } catch (error) {
          this.logger.warn(
            "ID token verification failed, using access token only:",
            error,
          );
        }
      }

      // Ensure we have an email
      if (typeof email !== "string" || email.length === 0) {
        throw new Error("No email found in tokens - ID token may be required");
      }

      return plainToInstance(CognitoUserDto, {
        sub: accessPayload.sub,
        email: email,
        emailVerified: emailVerified === true,
        exp: accessPayload.exp,
        iat: accessPayload.iat,
      });
    } catch (error) {
      this.logger.error("Error verifying tokens:", error);
      throw new UnauthorizedException("Invalid tokens");
    }
  }

  /**
   * Get authenticated user from request
   */
  async getUserFromRequest(request: Request): Promise<CognitoUserDto | null> {
    const { accessToken, idToken } = this.extractTokensFromRequest(request);

    if (accessToken.length === 0) {
      return null;
    }

    try {
      return await this.verifyTokens(accessToken, idToken);
    } catch {
      return null;
    }
  }
}
