import { registerAs } from "@nestjs/config";
import { IAWSConfig } from "../interfaces/aws.interface";

export default registerAs(
  "aws",
  (): IAWSConfig => ({
    region: process.env.AWS_REGION,
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    cognitoUserPoolId: process.env.COGNITO_USER_POOL_ID,
    cognitoClientId: process.env.COGNITO_CLIENT_ID,
    cognitoRegion: process.env.COGNITO_REGION,
  }),
);
