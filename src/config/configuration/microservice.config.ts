import { registerAs } from "@nestjs/config";
import { IMicroserviceConfig } from "../interfaces/microservice.interface";

export default registerAs("microservice", (): IMicroserviceConfig => {
  return {
    port: parseInt(process.env.MICROSERVICE_PORT ?? "3003", 10),
    name: process.env.SERVICE_NAME ?? "be-pricing-management",
    discoveryNamespace:
      process.env.SERVICE_DISCOVERY_NAMESPACE ??
      `ecom360-${process.env.NODE_ENV ?? "production"}.local`,
    clients: {
      shopService: {
        host: process.env.SHOP_MICROSERVICE_HOST ?? "0.0.0.0",
        port: parseInt(process.env.SHOP_MICROSERVICE_PORT ?? "3005", 10),
        timeout: parseInt(process.env.SHOP_SERVICE_TIMEOUT ?? "30000", 10),
      },
    },
  };
});
